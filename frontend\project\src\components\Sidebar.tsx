import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Plus, 
  History, 
  Music, 
  Folder, 
  Star, 
  Settings, 
  User, 
  Headphones,
  Waveform,
  Mic,
  Volume2,
  ChevronRight,
  Clock,
  Heart,
  Share2
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
  onToggle: (open: boolean) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const [activeTab, setActiveTab] = useState('create');

  const recentTracks = [
    { id: 1, title: 'Chill Lo-fi Vibes', genre: 'Lo-fi', duration: '2:34', liked: true },
    { id: 2, title: 'Epic Cinematic Score', genre: 'Cinematic', duration: '3:21', liked: false },
    { id: 3, title: 'Upbeat Hip-hop Beat', genre: 'Hip-hop', duration: '1:45', liked: true },
    { id: 4, title: 'Ambient Soundscape', genre: 'Ambient', duration: '4:12', liked: false },
  ];

  const collections = [
    { name: 'Favorites', count: 12, icon: Heart },
    { name: 'Lo-fi Collection', count: 8, icon: Music },
    { name: 'Workout Beats', count: 15, icon: Volume2 },
    { name: 'Study Music', count: 6, icon: Headphones },
  ];

  return (
    <>
      {/* Sidebar Toggle Button */}
      <motion.button
        onClick={() => onToggle(!isOpen)}
        className="fixed top-6 left-6 z-50 w-12 h-12 bg-glass backdrop-blur-lg border border-white/20 rounded-xl flex items-center justify-center text-white hover:bg-white/10 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <ChevronRight className="w-5 h-5" />
        </motion.div>
      </motion.button>

      {/* Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-30 lg:hidden"
              onClick={() => onToggle(false)}
            />

            {/* Sidebar Content */}
            <motion.div
              initial={{ x: -320, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -320, opacity: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="fixed left-0 top-0 h-full w-80 bg-glass backdrop-blur-xl border-r border-white/10 z-40 overflow-hidden"
            >
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="p-6 border-b border-white/10">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-r from-accent-from to-accent-to rounded-lg flex items-center justify-center">
                      <Waveform className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-lg font-semibold text-white">PromptBeat</h2>
                      <p className="text-sm text-white/60">AI Studio</p>
                    </div>
                  </div>
                </div>

                {/* Navigation Tabs */}
                <div className="flex border-b border-white/10">
                  {[
                    { id: 'create', label: 'Create', icon: Plus },
                    { id: 'history', label: 'History', icon: History },
                    { id: 'library', label: 'Library', icon: Folder },
                  ].map(({ id, label, icon: Icon }) => (
                    <motion.button
                      key={id}
                      onClick={() => setActiveTab(id)}
                      className={`flex-1 p-4 flex items-center justify-center space-x-2 transition-colors ${
                        activeTab === id 
                          ? 'text-accent-from border-b-2 border-accent-from bg-white/5' 
                          : 'text-white/60 hover:text-white hover:bg-white/5'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Icon className="w-4 h-4" />
                      <span className="text-sm font-medium">{label}</span>
                    </motion.button>
                  ))}
                </div>

                {/* Content */}
                <div className="flex-1 overflow-y-auto">
                  <AnimatePresence mode="wait">
                    {activeTab === 'create' && (
                      <motion.div
                        key="create"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="p-6 space-y-6"
                      >
                        {/* New Track Button */}
                        <motion.button
                          className="w-full p-4 bg-gradient-to-r from-accent-from to-accent-to rounded-xl text-white font-medium flex items-center justify-center space-x-2 hover:shadow-lg transition-shadow"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <Plus className="w-5 h-5" />
                          <span>New Track</span>
                        </motion.button>

                        {/* Quick Templates */}
                        <div>
                          <h3 className="text-white font-medium mb-3">Quick Start</h3>
                          <div className="space-y-2">
                            {[
                              { name: 'Lo-fi Chill', icon: '🎵', color: 'from-purple-500 to-pink-500' },
                              { name: 'Hip-hop Beat', icon: '🎤', color: 'from-red-500 to-orange-500' },
                              { name: 'EDM Drop', icon: '⚡', color: 'from-blue-500 to-cyan-500' },
                              { name: 'Ambient Pad', icon: '🌙', color: 'from-indigo-500 to-purple-500' },
                            ].map((template) => (
                              <motion.button
                                key={template.name}
                                className="w-full p-3 bg-white/5 hover:bg-white/10 rounded-lg flex items-center space-x-3 transition-colors"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                <div className={`w-8 h-8 bg-gradient-to-r ${template.color} rounded-lg flex items-center justify-center text-sm`}>
                                  {template.icon}
                                </div>
                                <span className="text-white/80 text-sm">{template.name}</span>
                              </motion.button>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {activeTab === 'history' && (
                      <motion.div
                        key="history"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="p-6"
                      >
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-white font-medium">Recent Tracks</h3>
                          <button className="text-white/60 hover:text-white text-sm">Clear All</button>
                        </div>
                        <div className="space-y-3">
                          {recentTracks.map((track) => (
                            <motion.div
                              key={track.id}
                              className="p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors cursor-pointer group"
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex-1">
                                  <h4 className="text-white text-sm font-medium truncate">{track.title}</h4>
                                  <div className="flex items-center space-x-2 mt-1">
                                    <span className="text-white/60 text-xs">{track.genre}</span>
                                    <div className="w-1 h-1 bg-white/40 rounded-full" />
                                    <span className="text-white/60 text-xs flex items-center">
                                      <Clock className="w-3 h-3 mr-1" />
                                      {track.duration}
                                    </span>
                                  </div>
                                </div>
                                <div className="flex items-center space-x-2">
                                  <motion.button
                                    className={`p-1 rounded ${track.liked ? 'text-red-400' : 'text-white/40 hover:text-red-400'}`}
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Heart className="w-4 h-4" fill={track.liked ? 'currentColor' : 'none'} />
                                  </motion.button>
                                  <motion.button
                                    className="p-1 text-white/40 hover:text-white opacity-0 group-hover:opacity-100 transition-opacity"
                                    whileHover={{ scale: 1.1 }}
                                    whileTap={{ scale: 0.9 }}
                                  >
                                    <Share2 className="w-4 h-4" />
                                  </motion.button>
                                </div>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </motion.div>
                    )}

                    {activeTab === 'library' && (
                      <motion.div
                        key="library"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="p-6 space-y-6"
                      >
                        {/* Collections */}
                        <div>
                          <h3 className="text-white font-medium mb-3">Collections</h3>
                          <div className="space-y-2">
                            {collections.map((collection) => (
                              <motion.button
                                key={collection.name}
                                className="w-full p-3 bg-white/5 hover:bg-white/10 rounded-lg flex items-center justify-between transition-colors"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                              >
                                <div className="flex items-center space-x-3">
                                  <collection.icon className="w-5 h-5 text-accent-from" />
                                  <span className="text-white/80 text-sm">{collection.name}</span>
                                </div>
                                <span className="text-white/60 text-xs">{collection.count}</span>
                              </motion.button>
                            ))}
                          </div>
                        </div>

                        {/* Stats */}
                        <div className="bg-white/5 rounded-lg p-4">
                          <h4 className="text-white font-medium mb-3">Your Stats</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-white/60 text-sm">Tracks Created</span>
                              <span className="text-white text-sm font-medium">47</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-white/60 text-sm">Total Playtime</span>
                              <span className="text-white text-sm font-medium">2h 34m</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-white/60 text-sm">Favorites</span>
                              <span className="text-white text-sm font-medium">12</span>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Footer */}
                <div className="p-6 border-t border-white/10">
                  <div className="flex items-center justify-between">
                    <motion.button
                      className="p-2 text-white/60 hover:text-white transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Settings className="w-5 h-5" />
                    </motion.button>
                    <motion.button
                      className="p-2 text-white/60 hover:text-white transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <User className="w-5 h-5" />
                    </motion.button>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default Sidebar;