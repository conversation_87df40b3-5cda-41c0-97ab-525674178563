#!/usr/bin/env python3
# pip install pyautogui pygetwindow

import pyautogui as pg
import pygetwindow as gw
import pathlib, random, time, platform, sys

# ——— KONFIG ———————————————————————————————————
PROJECT_DIR = pathlib.Path(r"D:\Projects\PromptBeatAI\frontend\project\src\components")
COMMENT     = "// auto-note"
EXT         = {".tsx", ".ts", ".jsx", ".js", ".html", ".css"}
MIN_WAIT, MAX_WAIT = 5, 120
# ————————————————————————————————————————

FILES = [p.relative_to(PROJECT_DIR).as_posix()
         for p in PROJECT_DIR.rglob("*")
         if p.suffix.lower() in EXT]

if not FILES:
    sys.exit("❌ Brak plików o podanych rozszerzeniach.")

# ========== Pomocnicze ========== #
def get_vscode_window():
    for w in gw.getAllWindows():
        if w.title and ("visual studio code" in w.title.lower()
                        or w.title.lower().endswith("- vscode")):
            return w
    return None

def open_file(file_rel: str):
    pg.hotkey("ctrl", "p")                      # Quick Open
    pg.typewrite(file_rel, interval=0.015)
    pg.press("enter")

def add_comment():
    pg.press("end")
    pg.typewrite(" " + COMMENT, interval=0.02)

def save():
    pg.hotkey("ctrl", "s") if platform.system() != "Darwin" \
        else pg.hotkey("command", "s")

# ========== Pętla główna ========== #
print("▶️  Bot działa w tle (Ctrl+C aby przerwać)…")

try:
    while True:
        vscode = get_vscode_window()
        if vscode:
            prev = gw.getActiveWindow()        # zapamiętaj aktywne okno
            vscode.activate()                  # przenieś VS Code na wierzch
            time.sleep(0.3)                    # ułamki sekundy na fokus

            target = random.choice(FILES)
            open_file(target)
            time.sleep(0.5)                    # wczytywanie pliku

            add_comment(); save()
            time.sleep(0.3)
            pg.hotkey("ctrl", "z"); save()

            print(f"[{time.strftime('%H:%M:%S')}]  {target} – add/save/undo/save")

            # przywróć wcześniejsze okno, jeśli istnieje
            if prev and prev != vscode:
                prev.activate()
        else:
            print("⚠️  VS Code nieotwarty – pomijam cykl.")

        time.sleep(random.randint(MIN_WAIT, MAX_WAIT))

except KeyboardInterrupt:
    print("\n⏹ Zakończono.")
