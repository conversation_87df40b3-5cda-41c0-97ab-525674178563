import React, { useState } from 'react';
import { motion } from 'framer-motion';

const StyleSelector: React.FC = () => {
  const [selectedStyle, setSelectedStyle] = useState('Lo-fi');
  
  const styles = [
    { 
      name: 'Lo-fi', 
      icon: '🎵',
      description: 'Chill & relaxed',
      image: 'https://images.pexels.com/photos/164938/pexels-photo-164938.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    { 
      name: 'Hip-hop', 
      icon: '🎤',
      description: 'Urban beats',
      image: 'https://images.pexels.com/photos/1190297/pexels-photo-1190297.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    { 
      name: 'EDM', 
      icon: '⚡',
      description: 'Electronic dance',
      image: 'https://images.pexels.com/photos/1763075/pexels-photo-1763075.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    { 
      name: 'Cinematic', 
      icon: '🎬',
      description: 'Epic & dramatic',
      image: 'https://images.pexels.com/photos/7991579/pexels-photo-7991579.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    { 
      name: 'Classical', 
      icon: '🎼',
      description: 'Orchestral',
      image: 'https://images.pexels.com/photos/164821/pexels-photo-164821.jpeg?auto=compress&cs=tinysrgb&w=400'
    },
    { 
      name: 'Ambient', 
      icon: '🌙',
      description: 'Atmospheric',
      image: 'https://images.pexels.com/photos/1105666/pexels-photo-1105666.jpeg?auto=compress&cs=tinysrgb&w=400'
    }
  ];

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
      {styles.map((style) => (
        <motion.button
          key={style.name}
          onClick={() => setSelectedStyle(style.name)}
          className={`relative p-4 rounded-xl border-2 transition-all overflow-hidden ${
            selectedStyle === style.name
              ? 'border-accent-from bg-white/10'
              : 'border-white/20 bg-white/5 hover:bg-white/10'
          }`}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Background Image */}
          <div className="absolute inset-0 opacity-20">
            <img 
              src={style.image} 
              alt={style.name}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
          </div>
          
          {/* Content */}
          <div className="relative z-10">
            <div className="text-2xl mb-2">{style.icon}</div>
            <h4 className="text-white font-medium mb-1">{style.name}</h4>
            <p className="text-white/60 text-xs">{style.description}</p>
          </div>
          
          {selectedStyle === style.name && (
            <motion.div
              className="absolute inset-0 border-2 border-accent-from rounded-xl"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.2 }}
            />
          )}
        </motion.button>
      ))}
    </div>
  );
};

export default StyleSelector;