import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { VisualSong, VisualNote, VisualTrack } from '../types/LoopmakerTypes';
import { LoopmakerParser } from '../utils/LoopmakerParser';

interface LoopmakerVisualizerProps {
  visualSong: VisualSong | null;
  currentTime: number;
  isPlaying: boolean;
  className?: string;
  type?: 'pianoRoll' | 'waveform' | 'circular' | 'spectrogram';
}

const LoopmakerVisualizer: React.FC<LoopmakerVisualizerProps> = ({
  visualSong,
  currentTime,
  isPlaying,
  className = '',
  type = 'pianoRoll'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 400 });
  const [parser, setParser] = useState<LoopmakerParser | null>(null);

  // Update canvas size on resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (canvasRef.current) {
        const rect = canvasRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height: rect.height });
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, []);

  // Piano Roll visualization
  const drawPianoRoll = useCallback((ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!visualSong) return;

    ctx.clearRect(0, 0, width, height);

    // Draw background grid
    drawGrid(ctx, width, height);

    // Draw tracks
    const trackHeight = height / Math.max(1, getAllTracks().length);
    let trackY = 0;

    for (const track of getAllTracks()) {
      drawTrack(ctx, track, trackY, trackHeight, width);
      trackY += trackHeight;
    }

    // Draw playhead
    drawPlayhead(ctx, width, height);
  }, [visualSong, currentTime]);

  const drawGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!visualSong) return;

    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;

    // Vertical lines (beats)
    const totalBeats = visualSong.totalBars * visualSong.beatsPerBar;
    const beatWidth = width / totalBeats;

    for (let beat = 0; beat <= totalBeats; beat++) {
      const x = beat * beatWidth;
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();

      // Stronger lines for bar boundaries
      if (beat % visualSong.beatsPerBar === 0) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.stroke();
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      }
    }

    // Horizontal lines (tracks)
    const tracks = getAllTracks();
    const trackHeight = height / Math.max(1, tracks.length);

    for (let i = 0; i <= tracks.length; i++) {
      const y = i * trackHeight;
      ctx.beginPath();
      ctx.moveTo(0, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawTrack = (
    ctx: CanvasRenderingContext2D, 
    track: VisualTrack, 
    trackY: number, 
    trackHeight: number, 
    width: number
  ) => {
    if (!visualSong) return;

    // Track background
    ctx.fillStyle = track.mute ? 'rgba(100, 100, 100, 0.1)' : `${track.color}20`;
    ctx.fillRect(0, trackY, width, trackHeight);

    // Track label
    ctx.fillStyle = track.mute ? 'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.8)';
    ctx.font = '12px Inter, sans-serif';
    ctx.fillText(track.name, 8, trackY + 20);

    // Draw notes
    const totalSteps = visualSong.totalBars * visualSong.beatsPerBar * visualSong.stepsPerBeat;
    const stepWidth = width / totalSteps;

    for (const note of track.notes) {
      drawNote(ctx, note, trackY, trackHeight, stepWidth, track);
    }
  };

  const drawNote = (
    ctx: CanvasRenderingContext2D,
    note: VisualNote,
    trackY: number,
    trackHeight: number,
    stepWidth: number,
    track: VisualTrack
  ) => {
    const x = note.step * stepWidth;
    const width = note.steps * stepWidth;
    const noteHeight = trackHeight * 0.6;
    const y = trackY + (trackHeight - noteHeight) / 2;

    // Note background
    const alpha = track.mute ? 0.3 : note.intensity * track.gain;
    ctx.fillStyle = `${note.color}${Math.floor(alpha * 255).toString(16).padStart(2, '0')}`;
    ctx.fillRect(x, y, width, noteHeight);

    // Note border
    ctx.strokeStyle = note.color;
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, noteHeight);

    // Note label (for longer notes)
    if (width > 30) {
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.font = '10px Inter, sans-serif';
      ctx.fillText(note.note, x + 4, y + noteHeight / 2 + 3);
    }

    // Active note highlight
    if (isNoteActive(note)) {
      ctx.strokeStyle = '#ffffff';
      ctx.lineWidth = 2;
      ctx.strokeRect(x - 1, y - 1, width + 2, noteHeight + 2);
      
      // Glow effect
      ctx.shadowColor = note.color;
      ctx.shadowBlur = 10;
      ctx.strokeRect(x - 1, y - 1, width + 2, noteHeight + 2);
      ctx.shadowBlur = 0;
    }
  };

  const drawPlayhead = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!visualSong) return;

    const progress = currentTime / visualSong.duration;
    const x = progress * width;

    // Playhead line
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.setLineDash([]);
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();

    // Playhead triangle
    ctx.fillStyle = '#ffffff';
    ctx.beginPath();
    ctx.moveTo(x - 8, 0);
    ctx.lineTo(x + 8, 0);
    ctx.lineTo(x, 16);
    ctx.closePath();
    ctx.fill();
  };

  const getAllTracks = (): VisualTrack[] => {
    if (!visualSong) return [];
    
    const allTracks: VisualTrack[] = [];
    const trackMap = new Map<string, VisualTrack>();

    for (const loop of visualSong.loops) {
      for (const track of loop.tracks) {
        if (!trackMap.has(track.id)) {
          trackMap.set(track.id, track);
        }
      }
    }

    return Array.from(trackMap.values());
  };

  const isNoteActive = (note: VisualNote): boolean => {
    if (!visualSong || !parser) return false;

    const activeNotes = parser.getNotesAtTime(visualSong, currentTime);
    return activeNotes.some(activeNote => 
      activeNote.id === note.id || 
      (activeNote.trackId === note.trackId && 
       activeNote.step === note.step && 
       activeNote.note === note.note)
    );
  };

  // Circular visualization
  const drawCircular = useCallback((ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!visualSong) return;

    ctx.clearRect(0, 0, width, height);

    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 4;
    const maxRadius = Math.min(width, height) / 2.5;

    // Draw tracks in concentric circles
    const tracks = getAllTracks();
    const radiusStep = (maxRadius - radius) / Math.max(1, tracks.length);

    tracks.forEach((track, trackIndex) => {
      const trackRadius = radius + (trackIndex * radiusStep);
      drawTrackCircular(ctx, track, centerX, centerY, trackRadius, radiusStep * 0.8);
    });

    // Draw center circle with BPM
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.5, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(111, 0, 255, 0.3)';
    ctx.fill();

    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Inter, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(`${visualSong.bpm}`, centerX, centerY - 5);
    ctx.font = '12px Inter, sans-serif';
    ctx.fillText('BPM', centerX, centerY + 15);
  }, [visualSong, currentTime]);

  const drawTrackCircular = (
    ctx: CanvasRenderingContext2D,
    track: VisualTrack,
    centerX: number,
    centerY: number,
    radius: number,
    thickness: number
  ) => {
    if (!visualSong) return;

    const totalSteps = visualSong.totalBars * visualSong.beatsPerBar * visualSong.stepsPerBeat;
    const angleStep = (Math.PI * 2) / totalSteps;

    // Draw track circle
    ctx.strokeStyle = `${track.color}40`;
    ctx.lineWidth = thickness;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.stroke();

    // Draw notes
    for (const note of track.notes) {
      const startAngle = note.step * angleStep - Math.PI / 2;
      const endAngle = (note.step + note.steps) * angleStep - Math.PI / 2;
      
      const alpha = track.mute ? 0.3 : note.intensity * track.gain;
      ctx.strokeStyle = `${note.color}${Math.floor(alpha * 255).toString(16).padStart(2, '0')}`;
      ctx.lineWidth = thickness * 0.8;
      
      if (isNoteActive(note)) {
        ctx.lineWidth = thickness;
        ctx.shadowColor = note.color;
        ctx.shadowBlur = 10;
      }

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.stroke();
      
      if (isNoteActive(note)) {
        ctx.shadowBlur = 0;
      }
    }
  };

  // Main draw function
  const draw = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !visualSong) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const { width, height } = canvasSize;
    
    // Set canvas size
    canvas.width = width * window.devicePixelRatio;
    canvas.height = height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    
    switch (type) {
      case 'pianoRoll':
        drawPianoRoll(ctx, width, height);
        break;
      case 'circular':
        drawCircular(ctx, width, height);
        break;
      default:
        drawPianoRoll(ctx, width, height);
    }
    
    if (isPlaying) {
      animationFrameRef.current = requestAnimationFrame(draw);
    }
  }, [canvasSize, type, isPlaying, drawPianoRoll, drawCircular]);

  // Start/stop animation
  useEffect(() => {
    if (isPlaying) {
      draw();
    } else if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isPlaying, draw]);

  // Initial draw and when song changes
  useEffect(() => {
    draw();
  }, [draw, visualSong]);

  return (
    <motion.div 
      className={`relative bg-black/20 rounded-xl overflow-hidden ${className}`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ 
          width: '100%', 
          height: '100%',
          filter: 'drop-shadow(0 0 10px rgba(111, 0, 255, 0.3))'
        }}
      />
      
      {/* Overlay effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/20 pointer-events-none" />
      
      {/* Type indicator */}
      <div className="absolute top-2 right-2 bg-black/50 backdrop-blur-sm rounded-lg px-2 py-1">
        <span className="text-white/60 text-xs capitalize">{type}</span>
      </div>

      {/* Current section indicator */}
      {visualSong && parser && (
        <div className="absolute top-2 left-2 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-1">
          <span className="text-white/80 text-xs font-medium">
            {parser.getCurrentSection(visualSong, currentTime)}
          </span>
        </div>
      )}
    </motion.div>
  );
};

export default LoopmakerVisualizer;
