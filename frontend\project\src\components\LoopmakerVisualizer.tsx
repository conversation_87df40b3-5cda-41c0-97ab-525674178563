import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { VisualSong, VisualNote, VisualTrack } from '../types/LoopmakerTypes';
import { LoopmakerParser } from '../utils/LoopmakerParser';

interface LoopmakerVisualizerProps {
  visualSong: VisualSong | null;
  currentTime: number;
  isPlaying: boolean;
  parser?: LoopmakerParser | null;
  className?: string;
  type?: 'pianoRoll' | 'waveform' | 'circular' | 'spectrogram';
}

const LoopmakerVisualizer: React.FC<LoopmakerVisualizerProps> = ({
  visualSong,
  currentTime,
  isPlaying,
  parser: externalParser,
  className = '',
  type = 'pianoRoll'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [canvasSize, setCanvasSize] = useState({ width: 800, height: 400 });

  // Update canvas size on resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (canvasRef.current) {
        const rect = canvasRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height: rect.height });
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, []);

  // Use external parser directly
  const parser = externalParser;

  // Piano Roll visualization
  const drawPianoRoll = useCallback((ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!visualSong) return;

    ctx.clearRect(0, 0, width, height);

    // Draw background
    drawBackground(ctx, width, height);

    // Draw timeline grid
    drawTimelineGrid(ctx, width, height);

    // Calculate layout
    const tracks = getAllTracks();
    const trackHeight = Math.max(60, (height - 80) / Math.max(1, tracks.length)); // Minimum 60px per track
    const headerHeight = 60;
    const leftPanelWidth = 200;

    // Draw track headers
    drawTrackHeaders(ctx, tracks, leftPanelWidth, headerHeight, trackHeight);

    // Draw tracks content
    drawTracksContent(ctx, tracks, leftPanelWidth, headerHeight, trackHeight, width);

    // Draw playhead
    drawPlayhead(ctx, width, height, leftPanelWidth);

    // Draw timeline ruler
    drawTimelineRuler(ctx, width, headerHeight, leftPanelWidth);
  }, [visualSong, currentTime]);

  const drawBackground = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Main background
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, '#1a1a2e');
    gradient.addColorStop(1, '#16213e');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  };

  const drawTimelineGrid = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!visualSong) return;

    const leftPanelWidth = 200;
    const headerHeight = 60;
    const contentWidth = width - leftPanelWidth;
    const contentHeight = height - headerHeight;

    // Calculate grid spacing
    const totalBeats = visualSong.totalBars * visualSong.beatsPerBar;
    const beatWidth = contentWidth / totalBeats;
    const stepWidth = beatWidth / visualSong.stepsPerBeat;

    // Draw vertical grid lines (steps and beats)
    for (let beat = 0; beat <= totalBeats; beat++) {
      const x = leftPanelWidth + (beat * beatWidth);

      // Beat lines
      ctx.strokeStyle = beat % visualSong.beatsPerBar === 0 ?
        'rgba(255, 255, 255, 0.3)' : 'rgba(255, 255, 255, 0.15)';
      ctx.lineWidth = beat % visualSong.beatsPerBar === 0 ? 2 : 1;

      ctx.beginPath();
      ctx.moveTo(x, headerHeight);
      ctx.lineTo(x, height);
      ctx.stroke();

      // Step subdivision lines (lighter)
      if (beat < totalBeats) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.05)';
        ctx.lineWidth = 1;

        for (let step = 1; step < visualSong.stepsPerBeat; step++) {
          const stepX = x + (step * stepWidth);
          ctx.beginPath();
          ctx.moveTo(stepX, headerHeight);
          ctx.lineTo(stepX, height);
          ctx.stroke();
        }
      }
    }

    // Draw horizontal track separator lines
    const tracks = getAllTracks();
    const trackHeight = Math.max(60, contentHeight / Math.max(1, tracks.length));

    for (let i = 0; i <= tracks.length; i++) {
      const y = headerHeight + (i * trackHeight);
      ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
      ctx.lineWidth = 1;

      ctx.beginPath();
      ctx.moveTo(leftPanelWidth, y);
      ctx.lineTo(width, y);
      ctx.stroke();
    }
  };

  const drawTrackHeaders = (
    ctx: CanvasRenderingContext2D,
    tracks: VisualTrack[],
    leftPanelWidth: number,
    headerHeight: number,
    trackHeight: number
  ) => {
    // Left panel background
    const gradient = ctx.createLinearGradient(0, 0, leftPanelWidth, 0);
    gradient.addColorStop(0, '#2a2a3e');
    gradient.addColorStop(1, '#1e1e32');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, headerHeight, leftPanelWidth, tracks.length * trackHeight);

    // Draw track headers
    tracks.forEach((track, index) => {
      const y = headerHeight + (index * trackHeight);

      // Track header background
      ctx.fillStyle = track.mute ? 'rgba(100, 100, 100, 0.2)' : `${track.color}30`;
      ctx.fillRect(0, y, leftPanelWidth, trackHeight);

      // Track color indicator
      ctx.fillStyle = track.color;
      ctx.fillRect(8, y + 8, 4, trackHeight - 16);

      // Track name
      ctx.fillStyle = track.mute ? 'rgba(255, 255, 255, 0.4)' : 'rgba(255, 255, 255, 0.9)';
      ctx.font = 'bold 14px Inter, sans-serif';
      ctx.fillText(track.name, 20, y + 20);

      // Track type and info
      ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
      ctx.font = '11px Inter, sans-serif';
      const typeText = track.type.charAt(0).toUpperCase() + track.type.slice(1);
      const infoText = track.waveform ? `${typeText} (${track.waveform})` : typeText;
      ctx.fillText(infoText, 20, y + 35);

      // Mute indicator
      if (track.mute) {
        ctx.fillStyle = 'rgba(255, 100, 100, 0.8)';
        ctx.font = 'bold 10px Inter, sans-serif';
        ctx.fillText('MUTED', leftPanelWidth - 50, y + 20);
      }

      // Volume level
      const volumeWidth = 60;
      const volumeHeight = 4;
      const volumeX = leftPanelWidth - volumeWidth - 10;
      const volumeY = y + trackHeight - 15;

      ctx.fillStyle = 'rgba(255, 255, 255, 0.2)';
      ctx.fillRect(volumeX, volumeY, volumeWidth, volumeHeight);

      ctx.fillStyle = track.color;
      ctx.fillRect(volumeX, volumeY, volumeWidth * track.gain, volumeHeight);
    });
  };

  const drawTracksContent = (
    ctx: CanvasRenderingContext2D,
    tracks: VisualTrack[],
    leftPanelWidth: number,
    headerHeight: number,
    trackHeight: number,
    width: number
  ) => {
    if (!visualSong) return;

    const contentWidth = width - leftPanelWidth;
    const totalSteps = visualSong.totalBars * visualSong.beatsPerBar * visualSong.stepsPerBeat;
    const stepWidth = contentWidth / totalSteps;

    tracks.forEach((track, trackIndex) => {
      const trackY = headerHeight + (trackIndex * trackHeight);

      // Track content background
      ctx.fillStyle = track.mute ? 'rgba(50, 50, 50, 0.1)' : 'rgba(255, 255, 255, 0.02)';
      ctx.fillRect(leftPanelWidth, trackY, contentWidth, trackHeight);

      // Draw notes for this track
      for (const note of track.notes) {
        drawNote(ctx, note, trackY, trackHeight, stepWidth, track, leftPanelWidth);
      }
    });
  };

  const drawNote = (
    ctx: CanvasRenderingContext2D,
    note: VisualNote,
    trackY: number,
    trackHeight: number,
    stepWidth: number,
    track: VisualTrack,
    leftPanelWidth: number
  ) => {
    const x = leftPanelWidth + (note.step * stepWidth);
    const noteWidth = note.steps * stepWidth;
    const noteHeight = Math.min(trackHeight * 0.7, 40); // Max height 40px
    const y = trackY + (trackHeight - noteHeight) / 2;

    const isActive = isNoteActive(note);
    const alpha = track.mute ? 0.4 : (isActive ? 1.0 : note.intensity * track.gain);

    // Note shadow/glow for active notes
    if (isActive) {
      ctx.shadowColor = note.color;
      ctx.shadowBlur = 15;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;
    }

    // Note background with gradient
    const gradient = ctx.createLinearGradient(x, y, x, y + noteHeight);
    const baseColor = hexToRgb(note.color);
    if (baseColor) {
      gradient.addColorStop(0, `rgba(${baseColor.r}, ${baseColor.g}, ${baseColor.b}, ${alpha})`);
      gradient.addColorStop(1, `rgba(${baseColor.r}, ${baseColor.g}, ${baseColor.b}, ${alpha * 0.7})`);
    }
    ctx.fillStyle = gradient;
    ctx.fillRect(x, y, noteWidth, noteHeight);

    // Note border
    ctx.strokeStyle = isActive ? '#ffffff' : note.color;
    ctx.lineWidth = isActive ? 2 : 1;
    ctx.strokeRect(x, y, noteWidth, noteHeight);

    // Reset shadow
    ctx.shadowBlur = 0;

    // Note label (for longer notes)
    if (noteWidth > 25) {
      ctx.fillStyle = isActive ? '#ffffff' : 'rgba(255, 255, 255, 0.9)';
      ctx.font = `${Math.min(12, noteHeight / 3)}px Inter, sans-serif`;
      ctx.textAlign = 'left';
      ctx.fillText(note.note, x + 4, y + noteHeight / 2 + 4);
    }
  };

  const drawPlayhead = (ctx: CanvasRenderingContext2D, width: number, height: number, leftPanelWidth: number) => {
    if (!visualSong) return;

    const progress = currentTime / visualSong.duration;
    const contentWidth = width - leftPanelWidth;
    const x = leftPanelWidth + (progress * contentWidth);
    const headerHeight = 60;

    // Playhead line with gradient
    const gradient = ctx.createLinearGradient(x, headerHeight, x, height);
    gradient.addColorStop(0, '#ff6b6b');
    gradient.addColorStop(0.5, '#4ecdc4');
    gradient.addColorStop(1, '#45b7d1');

    ctx.strokeStyle = gradient;
    ctx.lineWidth = 3;
    ctx.setLineDash([]);
    ctx.beginPath();
    ctx.moveTo(x, headerHeight);
    ctx.lineTo(x, height);
    ctx.stroke();

    // Playhead triangle at top
    ctx.fillStyle = '#ff6b6b';
    ctx.beginPath();
    ctx.moveTo(x - 10, headerHeight);
    ctx.lineTo(x + 10, headerHeight);
    ctx.lineTo(x, headerHeight + 20);
    ctx.closePath();
    ctx.fill();

    // Playhead glow effect
    ctx.shadowColor = '#ff6b6b';
    ctx.shadowBlur = 20;
    ctx.strokeStyle = '#ff6b6b';
    ctx.lineWidth = 1;
    ctx.stroke();
    ctx.shadowBlur = 0;
  };

  const drawTimelineRuler = (ctx: CanvasRenderingContext2D, width: number, headerHeight: number, leftPanelWidth: number) => {
    if (!visualSong) return;

    const contentWidth = width - leftPanelWidth;

    // Ruler background
    const gradient = ctx.createLinearGradient(0, 0, 0, headerHeight);
    gradient.addColorStop(0, '#2a2a3e');
    gradient.addColorStop(1, '#1e1e32');
    ctx.fillStyle = gradient;
    ctx.fillRect(leftPanelWidth, 0, contentWidth, headerHeight);

    // Time markers
    const totalBeats = visualSong.totalBars * visualSong.beatsPerBar;
    const beatWidth = contentWidth / totalBeats;

    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.font = '12px Inter, sans-serif';
    ctx.textAlign = 'center';

    for (let bar = 0; bar < visualSong.totalBars; bar++) {
      const x = leftPanelWidth + (bar * visualSong.beatsPerBar * beatWidth) + (visualSong.beatsPerBar * beatWidth / 2);
      ctx.fillText(`Bar ${bar + 1}`, x, 20);

      // Beat markers
      for (let beat = 0; beat < visualSong.beatsPerBar; beat++) {
        const beatX = leftPanelWidth + ((bar * visualSong.beatsPerBar + beat) * beatWidth);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
        ctx.font = '10px Inter, sans-serif';
        ctx.fillText(`${beat + 1}`, beatX + beatWidth / 2, 40);
      }
    }

    // Current time display
    const currentBar = Math.floor((currentTime / visualSong.duration) * visualSong.totalBars) + 1;
    const currentBeat = Math.floor(((currentTime / visualSong.duration) * totalBeats) % visualSong.beatsPerBar) + 1;

    ctx.fillStyle = '#ff6b6b';
    ctx.font = 'bold 14px Inter, sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(`${currentBar}.${currentBeat}`, leftPanelWidth + 10, headerHeight - 10);
  };

  // Helper function to convert hex to RGB
  const hexToRgb = (hex: string): { r: number; g: number; b: number } | null => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : null;
  };

  const getAllTracks = (): VisualTrack[] => {
    if (!visualSong) return [];

    const trackMap = new Map<string, VisualTrack>();

    for (const loop of visualSong.loops) {
      for (const track of loop.tracks) {
        if (!trackMap.has(track.id)) {
          trackMap.set(track.id, track);
        }
      }
    }

    return Array.from(trackMap.values());
  };

  const isNoteActive = (note: VisualNote): boolean => {
    if (!visualSong) return false;

    // Calculate if note should be active based on current time
    const totalSteps = visualSong.totalBars * visualSong.beatsPerBar * visualSong.stepsPerBeat;
    const stepDuration = visualSong.duration / totalSteps;
    const noteStartTime = note.step * stepDuration;
    const noteEndTime = noteStartTime + (note.steps * stepDuration);

    return currentTime >= noteStartTime && currentTime <= noteEndTime;
  };

  // Circular visualization
  const drawCircular = useCallback((ctx: CanvasRenderingContext2D, width: number, height: number) => {
    if (!visualSong) return;

    ctx.clearRect(0, 0, width, height);

    const centerX = width / 2;
    const centerY = height / 2;
    const radius = Math.min(width, height) / 4;
    const maxRadius = Math.min(width, height) / 2.5;

    // Draw tracks in concentric circles
    const tracks = getAllTracks();
    const radiusStep = (maxRadius - radius) / Math.max(1, tracks.length);

    tracks.forEach((track, trackIndex) => {
      const trackRadius = radius + (trackIndex * radiusStep);
      drawTrackCircular(ctx, track, centerX, centerY, trackRadius, radiusStep * 0.8);
    });

    // Draw center circle with BPM
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius * 0.5, 0, Math.PI * 2);
    ctx.fillStyle = 'rgba(111, 0, 255, 0.3)';
    ctx.fill();

    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Inter, sans-serif';
    ctx.textAlign = 'center';
    ctx.fillText(`${visualSong.bpm}`, centerX, centerY - 5);
    ctx.font = '12px Inter, sans-serif';
    ctx.fillText('BPM', centerX, centerY + 15);
  }, [visualSong, currentTime]);

  const drawTrackCircular = (
    ctx: CanvasRenderingContext2D,
    track: VisualTrack,
    centerX: number,
    centerY: number,
    radius: number,
    thickness: number
  ) => {
    if (!visualSong) return;

    const totalSteps = visualSong.totalBars * visualSong.beatsPerBar * visualSong.stepsPerBeat;
    const angleStep = (Math.PI * 2) / totalSteps;

    // Draw track circle
    ctx.strokeStyle = `${track.color}40`;
    ctx.lineWidth = thickness;
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
    ctx.stroke();

    // Draw notes
    for (const note of track.notes) {
      const startAngle = note.step * angleStep - Math.PI / 2;
      const endAngle = (note.step + note.steps) * angleStep - Math.PI / 2;
      
      const alpha = track.mute ? 0.3 : note.intensity * track.gain;
      ctx.strokeStyle = `${note.color}${Math.floor(alpha * 255).toString(16).padStart(2, '0')}`;
      ctx.lineWidth = thickness * 0.8;
      
      if (isNoteActive(note)) {
        ctx.lineWidth = thickness;
        ctx.shadowColor = note.color;
        ctx.shadowBlur = 10;
      }

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.stroke();
      
      if (isNoteActive(note)) {
        ctx.shadowBlur = 0;
      }
    }
  };

  // Main draw function
  const draw = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !visualSong) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    const { width, height } = canvasSize;
    
    // Set canvas size
    canvas.width = width * window.devicePixelRatio;
    canvas.height = height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    
    switch (type) {
      case 'pianoRoll':
        drawPianoRoll(ctx, width, height);
        break;
      case 'circular':
        drawCircular(ctx, width, height);
        break;
      default:
        drawPianoRoll(ctx, width, height);
    }
    
    if (isPlaying) {
      animationFrameRef.current = requestAnimationFrame(draw);
    }
  }, [canvasSize, type, isPlaying, drawPianoRoll, drawCircular]);

  // Start/stop animation
  useEffect(() => {
    if (isPlaying) {
      draw();
    } else if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isPlaying, draw]);

  // Initial draw and when song changes
  useEffect(() => {
    draw();
  }, [draw, visualSong]);

  return (
    <motion.div 
      className={`relative bg-black/20 rounded-xl overflow-hidden ${className}`}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <canvas
        ref={canvasRef}
        className="w-full h-full"
        style={{ 
          width: '100%', 
          height: '100%',
          filter: 'drop-shadow(0 0 10px rgba(111, 0, 255, 0.3))'
        }}
      />
      
      {/* Overlay effects */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/20 pointer-events-none" />
      
      {/* Type indicator */}
      <div className="absolute top-2 right-2 bg-black/50 backdrop-blur-sm rounded-lg px-2 py-1">
        <span className="text-white/60 text-xs capitalize">{type}</span>
      </div>

      {/* Current section indicator */}
      {visualSong && (
        <div className="absolute top-2 left-2 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-1">
          <span className="text-white/80 text-xs font-medium">
            Bar {Math.floor((currentTime / visualSong.duration) * visualSong.totalBars) + 1} / {visualSong.totalBars}
          </span>
        </div>
      )}

      {/* Active tracks indicator */}
      {visualSong && type === 'pianoRoll' && (
        <div className="absolute bottom-2 left-2 bg-black/50 backdrop-blur-sm rounded-lg px-3 py-2 max-w-xs">
          <div className="text-white/60 text-xs mb-1">Active Tracks:</div>
          <div className="flex flex-wrap gap-1">
            {getAllTracks().map(track => {
              const hasActiveNotes = track.notes.some(note => isNoteActive(note));
              return (
                <div
                  key={track.id}
                  className={`px-2 py-1 rounded text-xs transition-all ${
                    hasActiveNotes
                      ? 'bg-white/20 text-white border border-white/30'
                      : 'bg-white/5 text-white/40'
                  }`}
                  style={{
                    borderColor: hasActiveNotes ? track.color : 'transparent',
                    backgroundColor: hasActiveNotes ? `${track.color}30` : undefined
                  }}
                >
                  {track.name}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default LoopmakerVisualizer;
