import React, { useState, useCallback, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Settings, Eye, BarChart3, Radio, Zap, Music2 } from 'lucide-react';
import RealAudioPlayer from './RealAudioPlayer';
import RealAudioVisualizer from './RealAudioVisualizer';
import LoopmakerVisualizer from './LoopmakerVisualizer';
import { FloatingMusicIcons, RotatingMusicIcons } from './MusicIcons';
import { VisualSong } from '../types/LoopmakerTypes';
import { loadSongFromJSON, LoopmakerParser } from '../utils/LoopmakerParser';

interface EnhancedMusicCanvasProps {
  isGenerating: boolean;
  audioSrc?: string;
  songDataSrc?: string;
  title?: string;
  artist?: string;
  bpm?: number;
}

const EnhancedMusicCanvas: React.FC<EnhancedMusicCanvasProps> = ({
  isGenerating,
  audioSrc = "/beat-freestyle.mp3",
  songDataSrc = "/beat-freestyle.json",
  title = "Beat for Freestyle",
  artist = "PromptBeat AI",
  bpm = 128
}) => {
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [frequencyData, setFrequencyData] = useState<Uint8Array>(new Uint8Array(256));
  const [isPlaying, setIsPlaying] = useState(false);
  const [visualizerType, setVisualizerType] = useState<'waveform' | 'frequency' | 'circular' | 'spectrogram' | 'pianoRoll'>('pianoRoll');
  const [showSettings, setShowSettings] = useState(false);
  const [visualSong, setVisualSong] = useState<VisualSong | null>(null);
  const [parser, setParser] = useState<LoopmakerParser | null>(null);

  const handleTimeUpdate = useCallback((time: number, dur: number) => {
    setCurrentTime(time);
    setDuration(dur);
  }, []);

  const handleFrequencyData = useCallback((data: Uint8Array) => {
    setFrequencyData(data);
  }, []);

  const handlePlayStateChange = useCallback((playing: boolean) => {
    setIsPlaying(playing);
  }, []);

  // Load song data
  useEffect(() => {
    const loadSongData = async () => {
      try {
        const songData = await loadSongFromJSON(songDataSrc);
        setVisualSong(songData);

        // Create parser for real-time analysis
        const response = await fetch(songDataSrc);
        const rawSongData = await response.json();
        const newParser = new LoopmakerParser(rawSongData);
        setParser(newParser);

        console.log('Loaded song data:', songData);
        console.log('Total duration:', songData.duration, 'seconds');
        console.log('Total tracks:', songData.loops.flatMap(l => l.tracks).length);
      } catch (error) {
        console.error('Failed to load song data:', error);
        // Fallback to basic visualization
        setVisualSong(null);
        setParser(null);
      }
    };

    loadSongData();
  }, [songDataSrc]);

  const visualizerTypes = [
    { type: 'pianoRoll' as const, icon: Music2, label: 'Piano Roll' },
    { type: 'waveform' as const, icon: BarChart3, label: 'Waveform' },
    { type: 'frequency' as const, icon: Radio, label: 'Frequency' },
    { type: 'circular' as const, icon: Zap, label: 'Circular' },
    { type: 'spectrogram' as const, icon: Eye, label: 'Spectrogram' }
  ];

  return (
    <motion.section 
      className="my-16"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
    >
      <div className="relative bg-glass backdrop-blur-lg border border-white/10 rounded-2xl overflow-hidden">
        {/* Header with BPM and Controls */}
        <div className="relative p-6 border-b border-white/10">
          <div className="flex items-center justify-between">
            {/* BPM Badge */}
            <motion.div
              className="flex items-center gap-3"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.5, type: "spring" }}
            >
              <div className="bg-gradient-to-r from-accent-from to-accent-to rounded-full px-4 py-2 border border-white/20 flex items-center gap-2">
                <RotatingMusicIcons.Disc 
                  size={16} 
                  color="#ffffff" 
                  speed={isPlaying ? (bpm / 60) : 0} 
                />
                <span className="text-white font-medium text-sm">{bpm} BPM</span>
              </div>
              
              {/* Track Info */}
              <div className="hidden md:block">
                <h3 className="text-white font-semibold">{title}</h3>
                <p className="text-white/60 text-sm">{artist}</p>
              </div>
            </motion.div>

            {/* Visualizer Controls */}
            <div className="flex items-center gap-2">
              {/* Visualizer Type Selector */}
              <div className="flex items-center bg-black/20 rounded-lg p-1">
                {visualizerTypes.map(({ type, icon: Icon, label }) => (
                  <motion.button
                    key={type}
                    onClick={() => setVisualizerType(type)}
                    className={`p-2 rounded-md transition-all ${
                      visualizerType === type
                        ? 'bg-accent-from text-white'
                        : 'text-white/60 hover:text-white hover:bg-white/10'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    title={label}
                  >
                    <Icon className="w-4 h-4" />
                  </motion.button>
                ))}
              </div>

              {/* Settings */}
              <motion.button
                onClick={() => setShowSettings(!showSettings)}
                className={`p-2 rounded-lg transition-colors ${
                  showSettings ? 'bg-accent-from text-white' : 'text-white/60 hover:text-white hover:bg-white/10'
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Settings className="w-4 h-4" />
              </motion.button>
            </div>
          </div>

          {/* Settings Panel */}
          <AnimatePresence>
            {showSettings && (
              <motion.div
                className="mt-4 p-4 bg-black/20 rounded-lg border border-white/10"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-white/60 text-xs mb-1">Sensitivity</label>
                    <input 
                      type="range" 
                      min="0.1" 
                      max="2" 
                      step="0.1" 
                      defaultValue="1"
                      className="w-full h-1 bg-white/20 rounded-full appearance-none cursor-pointer"
                    />
                  </div>
                  <div>
                    <label className="block text-white/60 text-xs mb-1">Smoothing</label>
                    <input 
                      type="range" 
                      min="0" 
                      max="1" 
                      step="0.1" 
                      defaultValue="0.8"
                      className="w-full h-1 bg-white/20 rounded-full appearance-none cursor-pointer"
                    />
                  </div>
                  <div>
                    <label className="block text-white/60 text-xs mb-1">Color Mode</label>
                    <select className="w-full bg-black/20 border border-white/20 rounded px-2 py-1 text-white text-xs">
                      <option>Rainbow</option>
                      <option>Monochrome</option>
                      <option>Gradient</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-white/60 text-xs mb-1">Quality</label>
                    <select className="w-full bg-black/20 border border-white/20 rounded px-2 py-1 text-white text-xs">
                      <option>High</option>
                      <option>Medium</option>
                      <option>Low</option>
                    </select>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Floating music icons around the canvas */}
        <div className="absolute inset-0 pointer-events-none z-10">
          <FloatingMusicIcons.Piano
            color="#a855f7"
            size={14}
            delay={0}
            duration={4}
            className="top-[20%] left-[10%]"
          />
          <FloatingMusicIcons.Guitar
            color="#ef4444"
            size={12}
            delay={1}
            duration={3.5}
            className="top-[60%] right-[15%]"
          />
          <FloatingMusicIcons.Drum
            color="#06b6d4"
            size={16}
            delay={2}
            duration={4.5}
            className="bottom-[30%] left-[20%]"
          />
          <FloatingMusicIcons.Mic
            color="#f59e0b"
            size={13}
            delay={0.5}
            duration={3.8}
            className="top-[40%] right-[25%]"
          />
          <FloatingMusicIcons.Music
            color="#10b981"
            size={15}
            delay={1.5}
            duration={4.2}
            className="bottom-[50%] right-[10%]"
          />
        </div>

        {/* Main Visualizer */}
        <div className="relative h-80 lg:h-96 p-6">
          {visualizerType === 'pianoRoll' || visualizerType === 'circular' ? (
            <LoopmakerVisualizer
              visualSong={visualSong}
              currentTime={currentTime}
              isPlaying={isPlaying}
              parser={parser}
              type={visualizerType === 'pianoRoll' ? 'pianoRoll' : 'circular'}
              className="w-full h-full"
            />
          ) : (
            <RealAudioVisualizer
              frequencyData={frequencyData}
              currentTime={currentTime}
              duration={duration}
              isPlaying={isPlaying}
              type={visualizerType}
              className="w-full h-full"
            />
          )}

          {/* Generation Loading Overlay */}
          {isGenerating && (
            <motion.div
              className="absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <div className="text-center">
                <motion.div
                  className="w-16 h-16 border-4 border-accent-from/30 border-t-accent-from rounded-full mx-auto mb-4"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
                <p className="text-white font-medium">Generating your music...</p>
                <p className="text-white/60 text-sm mt-1">This may take a few moments</p>
              </div>
            </motion.div>
          )}

          {/* Time Progress Indicator */}
          {duration > 0 && (
            <div className="absolute bottom-2 left-6 right-6">
              <div className="flex items-center justify-between text-xs text-white/60 mb-1">
                <span>{Math.floor(currentTime / 60)}:{Math.floor(currentTime % 60).toString().padStart(2, '0')}</span>
                <span>{Math.floor(duration / 60)}:{Math.floor(duration % 60).toString().padStart(2, '0')}</span>
              </div>
              <div className="h-1 bg-white/10 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-accent-from to-accent-to rounded-full"
                  style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                  animate={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                  transition={{ duration: 0.1 }}
                />
              </div>
            </div>
          )}
        </div>

        {/* Audio Player */}
        <div className="p-6 border-t border-white/10">
          <RealAudioPlayer
            audioSrc={audioSrc}
            title={title}
            artist={artist}
            onTimeUpdate={handleTimeUpdate}
            onFrequencyData={handleFrequencyData}
            onPlayStateChange={handlePlayStateChange}
          />
        </div>
      </div>
    </motion.section>
  );
};

export default EnhancedMusicCanvas;
