@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

:root {
  --accent-from: #6f00ff;
  --accent-to: #009dff;
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-black text-white font-sans;
    font-family: 'Inter', 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-white/5;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--accent-from), var(--accent-to));
    @apply rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply opacity-80;
  }
}

@layer components {
  .bg-glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
  }

  .ghost-button {
    @apply relative px-6 py-3 bg-transparent border-2 border-white/20 rounded-xl text-white font-medium transition-all duration-300 hover:border-white/40 hover:bg-white/5 hover:shadow-lg;
  }

  .ghost-button::before {
    content: '';
    @apply absolute inset-0 rounded-xl opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg, var(--accent-from), var(--accent-to));
  }

  .ghost-button:hover::before {
    @apply opacity-10;
  }

  .generate-button {
    @apply relative px-10 py-5 bg-gradient-to-r from-accent-from to-accent-to rounded-2xl text-white font-bold text-xl flex items-center space-x-3 shadow-2xl transition-all duration-300 hover:shadow-accent-from/25 hover:scale-105;
  }

  .generate-button::before {
    content: '';
    @apply absolute inset-0 rounded-2xl blur-lg opacity-50;
    background: linear-gradient(135deg, var(--accent-from), var(--accent-to));
  }

  .generate-button:hover::before {
    @apply opacity-70;
  }

  .generate-button > * {
    @apply relative z-10;
  }

  .hint-chip {
    @apply relative px-4 py-2 bg-glass backdrop-blur-md border border-white/20 rounded-full text-white/80 text-sm font-medium transition-all duration-300 hover:border-accent-from/50 hover:text-white hover:bg-white/10;
  }

  .hint-chip::before {
    content: '';
    @apply absolute inset-0 rounded-full opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg, var(--accent-from), var(--accent-to));
  }

  .hint-chip:hover::before {
    @apply opacity-20;
  }

  .hint-chip > * {
    @apply relative z-10;
  }

  .action-button {
    @apply relative px-6 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white font-medium transition-all duration-300 hover:border-accent-from/50 hover:bg-white/20 hover:scale-105 flex items-center space-x-2;
  }

  .action-button::before {
    content: '';
    @apply absolute inset-0 rounded-xl opacity-0 transition-opacity duration-300;
    background: linear-gradient(135deg, var(--accent-from), var(--accent-to));
  }

  .action-button:hover::before {
    @apply opacity-10;
  }

  .action-button > * {
    @apply relative z-10;
  }
}

@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, var(--accent-from), var(--accent-to));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(5px) rotate(-1deg);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(8px) rotate(-1deg);
  }
  66% {
    transform: translateY(-5px) rotate(1deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(111, 0, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 157, 255, 0.5);
  }
}

@keyframes hue-rotate {
  0% {
    filter: hue-rotate(0deg);
  }
  100% {
    filter: hue-rotate(360deg);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-hue-rotate {
  animation: hue-rotate 3s linear infinite;
}

/* Responsive design improvements */
@media (max-width: 768px) {
  .generate-button {
    @apply px-8 py-4 text-lg;
  }

  .ghost-button {
    @apply px-4 py-2 text-sm;
  }

  .action-button {
    @apply px-4 py-2 text-sm;
  }
}

/* Focus states for accessibility */
.ghost-button:focus,
.generate-button:focus,
.hint-chip:focus,
.action-button:focus {
  @apply outline-none ring-2 ring-accent-from/50 ring-offset-2 ring-offset-black;
}

/* Smooth transitions for all interactive elements */
button, input, textarea {
  @apply transition-all duration-200;
}

/* Range input styling */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

input[type="range"]::-webkit-slider-track {
  background: transparent;
  height: 100%;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  border: none;
  height: 100%;
  width: 100%;
}

input[type="range"]::-moz-range-track {
  background: transparent;
  height: 100%;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  background: transparent;
  border: none;
  height: 100%;
  width: 100%;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-glass {
    background: rgba(255, 255, 255, 0.15);
  }
  
  .ghost-button {
    @apply border-white/50;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}